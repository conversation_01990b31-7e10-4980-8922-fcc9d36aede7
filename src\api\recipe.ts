import request from '../utils/axios'

// 后端API返回的数据格式
interface ApiChannel {
  channel_id: number
  amount: number
  channel_no?: string
  channel_name?: string
  max_capacity?: number
  warn_capacity?: number
  mode?: string
}
interface ApiRecipe {
  id: number
  name: string
  channels: ApiChannel[]
}
interface ApiRecipeResponse {
  code: number
  message: string
  data: ApiRecipe[]
}

// 前端使用的数据格式
export interface Channel {
  channel_id: number
  amount: number
  channel_no?: string
  channel_name?: string
  max_capacity?: number
  warn_capacity?: number
  mode?: string
}
export interface Recipe {
  id: number
  name: string
  channels: Channel[]
}

// 转换函数：API格式 -> 前端格式
const toFrontendFormat = (apiRecipe: ApiRecipe): Recipe => {
  return {
    id: apiRecipe.id,
    name: apiRecipe.name,
    channels: apiRecipe.channels.map((c) => ({
      channel_id: c.channel_id,
      amount: c.amount,
      channel_no: c.channel_no,
      channel_name: c.channel_name,
      max_capacity: c.max_capacity,
      warn_capacity: c.warn_capacity,
      mode: c.mode,
    })),
  }
}

// 转换函数：前端格式 -> API格式
const toApiFormat = (recipe: Partial<Recipe> & { name: string }): Omit<ApiRecipe, 'id'> => {
  return {
    name: recipe.name,
    channels: (recipe.channels ?? []).map((c) => ({
      channel_id: c.channel_id,
      amount: c.amount,
      channel_no: c.channel_no,
      channel_name: c.channel_name,
      max_capacity: c.max_capacity,
      warn_capacity: c.warn_capacity,
      mode: c.mode,
    })),
  }
}

export const getRecipes = async (): Promise<Recipe[]> => {
  return request.get('/recipe')
}

export const deleteRecipes = async (recipe: Recipe) => {
  return request.delete(`/recipe/${recipe.id}`)
}

export const addRecipe = (recipe: Omit<Recipe, 'id'>) => {
  return request.post('/recipe', recipe)
}

export const updateRecipe = (recipe: Recipe) => {
  return request.put(`/recipe/${recipe.id}`, recipe)
}
