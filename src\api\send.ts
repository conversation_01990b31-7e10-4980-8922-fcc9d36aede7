import { showFailToast, showSuccessToast } from 'vant'
import request from '../utils/axios'

/**
 * 通用发送命令接口
 * @param data 请求体对象，结构需符合后端要求
 * @returns 后端响应
 */
export const sendCommand = (data: any): Promise<any> => {
  return request.post('/send', data)
}

export interface makeMilkTeaReq {
  device: string
  command: string
  pump_list: PumpList[]
}

export interface PumpList {
  pump_id: string
  weight: string
  mode: string //mode  = 'Normal','Combine','Suck_back',3种出货模式
}
/**
 * 出茶操作
 * @param pump_list 泵参数列表
 * @returns Promise<void>
 */
export const makeMilkTea = async (pump_list: PumpList[]) => {
  const req: makeMilkTeaReq = {
    device: 'robo',
    command: 'make_one_milk_tea',
    pump_list,
  }
  return sendCommand({ req })
}

/**
 * 轮询出茶结果
 * @param maxTries 最大轮询次数
 * @param interval 间隔(ms)
 * @returns Promise<boolean> 是否完成
 */
export const pollMakeTeaResult = async (maxTries = 30, interval = 2000): Promise<boolean> => {
  let tries = 0
  while (tries < maxTries) {
    try {
      const res = await sendCommand({
        req: {
          device: 'robo',
          command: 'poll',
        },
      })
      // 修改判断条件，兼容后端返回格式
      console.log(res)

      if (res?.rep?.result === 'success') {
        showSuccessToast('出茶完成')
        return true
      } else if (res?.rep?.result === 'error') {
        showFailToast(res?.rep?.desc || '操作失败')
        // 失败处理
        return false
      }
    } catch (e) {
      // 忽略单次异常
    }
    await new Promise((resolve) => setTimeout(resolve, interval))
    tries++
  }
  return false
}

/**
 * 校准测试1 - dispense_calib
 * @param pumpId 泵编号
 * @returns Promise<number> 实际重量
 */
export const dispenseCalibrateTest = async (pumpId: string): Promise<number> => {
  const req = {
    device: 'robo',
    command: 'dispense_calib',
    pump: pumpId,
    step: [50000, 100000], // 100000 steps, about 160g water flow out of the pipe
  }

  try {
    // 发送校准命令
    await sendCommand({ req })

    // 轮询结果
    const result = await pollCalibrateResult()
    return result
  } catch (error) {
    console.error('校准测试1失败:', error)
    throw error
  }
}

/**
 * 校准测试2 - dispense_by_step
 * @param pumpId 泵编号
 * @returns Promise<number> 实际重量
 */
export const dispenseByStepTest = async (pumpId: string): Promise<number> => {
  const req = {
    device: 'robo',
    command: 'dispense_by_step',
    pump: pumpId,
    step: 100000, // 100000 steps, about 160g water flow out of the pipe
  }

  try {
    // 发送测试命令
    await sendCommand({ req })

    // 轮询结果
    const result = await pollCalibrateResult()
    return result
  } catch (error) {
    console.error('校准测试2失败:', error)
    throw error
  }
}

/**
 * 轮询校准结果
 * @param maxTries 最大轮询次数
 * @param interval 间隔(ms)
 * @returns Promise<number> 重量结果
 */
export const pollCalibrateResult = async (maxTries = 30, interval = 2000): Promise<number> => {
  let tries = 0
  while (tries < maxTries) {
    try {
      const res = await sendCommand({
        req: {
          device: 'robo',
          command: 'poll',
        },
      })

      console.log('校准轮询结果:', res)

      // 检查是否成功并返回重量数据
      // 根据文档，响应格式为: {"result":"success", "meta": "ok", "desc": "ok","data":{'weight':weight}}
      // 由于axios拦截器返回res.data，所以res就是响应的数据部分
      if (res?.result === 'success' && res?.data?.weight !== undefined) {
        return res.data.weight
      } else if (res?.result === 'error') {
        throw new Error(res?.desc || '校准操作失败')
      }

      // 兼容旧格式 (rep格式)
      if (res?.rep?.result === 'success' && res?.rep?.data?.weight !== undefined) {
        return res.rep.data.weight
      } else if (res?.rep?.result === 'error') {
        throw new Error(res?.rep?.desc || '校准操作失败')
      }
    } catch (e) {
      console.warn('轮询异常:', e)
    }
    await new Promise((resolve) => setTimeout(resolve, interval))
    tries++
  }
  throw new Error('校准超时，请重试')
}

/**
 * 获取实时重量
 * @returns Promise<number> 当前重量
 */
export const getCurrentWeight = async (): Promise<number> => {
  const req = {
    device: 'robo',
    command: 'get_weight',
  }

  try {
    const res = await sendCommand({ req })

    // 根据您提供的API格式，响应为 rep.weight
    if (res?.rep?.result === 'success' && res?.rep?.weight !== undefined) {
      return parseFloat(res.rep.weight)
    }

    // 如果没有获取到重量数据，返回0
    return 0
  } catch (error) {
    console.warn('获取重量失败:', error)
    return 0
  }
}

/**
 * 设置重量（归零）
 * @param weight 要设置的重量值，通常为"0"进行归零
 * @returns Promise<boolean> 设置是否成功
 */
export const setWeight = async (weight: string = '0'): Promise<boolean> => {
  const req = {
    device: 'robo',
    command: 'set_weight',
    weight: weight,
  }

  try {
    const res = await sendCommand({ req })

    // 根据您提供的API格式检查响应
    if (res?.rep?.result === 'success') {
      return true
    }

    return false
  } catch (error) {
    console.warn('设置重量失败:', error)
    return false
  }
}

/**
 * 称重校准步骤1 - 清空杯架
 * @returns Promise<number> 实际重量
 */
export const weightCalibrateStep1 = async (): Promise<number> => {
  const req = {
    device: 'robo',
    command: 'dispense_calib',
    step: [50000, 100000], // 清空杯架的步骤
  }

  try {
    // 发送校准命令
    await sendCommand({ req })

    // 轮询结果
    const result = await pollCalibrateResult()
    return result
  } catch (error) {
    console.error('称重校准步骤1失败:', error)
    throw error
  }
}

/**
 * 称重校准步骤2 - 放入500g砝码
 * @returns Promise<number> 实际重量
 */
export const weightCalibrateStep2 = async (): Promise<number> => {
  const req = {
    device: 'robo',
    command: 'dispense_by_step',
    step: 100000, // 500g砝码校准步骤
  }

  try {
    // 发送测试命令
    await sendCommand({ req })

    // 轮询结果
    const result = await pollCalibrateResult()
    return result
  } catch (error) {
    console.error('称重校准步骤2失败:', error)
    throw error
  }
}

/**
 * 保存称重校准结果
 * @param step1Weight 步骤1重量
 * @param step2Weight 步骤2重量
 * @returns Promise<boolean> 保存是否成功
 */
export const saveWeightCalibration = async (
  step1Weight: number,
  step2Weight: number,
): Promise<boolean> => {
  const req = {
    device: 'robo',
    command: 'save_weight_calibration',
    step1_weight: step1Weight,
    step2_weight: step2Weight,
  }

  try {
    const res = await sendCommand({ req })
    // 检查新格式响应
    if (res?.result === 'success') {
      return true
    }
    // 兼容旧格式
    if (res?.rep?.result === 'success') {
      return true
    }
    return false
  } catch (error) {
    console.warn('保存称重校准结果失败:', error)
    // 模拟成功
    return true
  }
}

/**
 * 校准确认
 * @param channelNo 通道编号
 * @param test1Weight 测试1重量
 * @param test2Weight 测试2重量
 * @returns Promise<boolean> 校准是否成功
 */
export const confirmCalibration = async (
  channelNo: string,
  test1Weight: number,
  test2Weight: number,
): Promise<boolean> => {
  const req = {
    device: 'robo',
    command: 'confirm_calibration',
    channel_no: channelNo,
    test1_weight: test1Weight,
    test2_weight: test2Weight,
  }

  try {
    const res = await sendCommand({ req })
    // 检查新格式响应
    if (res?.result === 'success') {
      return true
    }
    // 兼容旧格式
    if (res?.rep?.result === 'success') {
      return true
    }
    return false
  } catch (error) {
    console.warn('校准确认API调用失败:', error)
    // 模拟成功
    return true
  }
}
