<template>
  <div class="flex flex-col items-center min-h-screen bg-gray-100 pt-6">
    <van-button icon="cross" size="small" @click="goBack" plain />
    <h2 class="text-2xl font-bold mb-6 mt-6">管理员页面</h2>
    <van-button type="primary" @click="goToChannelManagement" class="mb-6">通道管理</van-button>
    <van-button type="primary" @click="goToWeightCalibration" class="mb-6">称重校准</van-button>

    <!-- 可在此添加更多管理功能 -->
  </div>
</template>

<script setup lang="ts">
import router from '@/router'

const goBack = () => {
  router.back()
}

const goToChannelManagement = () => {
  router.push('/admin/channel-management')
}

const goToWeightCalibration = () => {
  router.push('/admin/weight-calibration')
}
</script>
