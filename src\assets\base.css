/* 注意：为什么要写两个重复的 :root？ */
/* 由于 vant 中的主题变量也是在 :root 下声明的，所以在有些情况下会由于优先级的问题无法成功覆盖。通过 :root:root 可以显式地让你所写内容的优先级更高一些，从而确保主题变量的成功覆盖。 */
:root:root {
  --van-font-size-md: 24px;
  --van-cell-line-height: 40px;
  --van-line-height-md: 40px;
  --van-toast-default-width: 20rem;
  --van-font-size-lg: 24px;
  /* fix bug https://github.com/youzan/vant/issues/13268 */

  .van-popup.van-toast {
    background: var(--van-toast-background) !important;
  }
}

body {
  min-height: 100vh;
  font-size: var(--van-font-size-md);
}

@import 'tailwindcss';
