<template>
  <div class="flex flex-col min-h-screen bg-gray-100">
    <!-- 顶部导航栏 -->
    <div class="flex justify-between items-center p-4 bg-white shadow-sm">
      <h2 class="text-xl font-bold">称重校准</h2>
      <van-button icon="arrow-left" size="small" @click="goBack" plain />
    </div>

    <!-- 主要内容区域 -->
    <div class="flex-1 p-6">
      <!-- 实时重量显示 -->
      <div class="bg-white rounded-lg p-6 mb-6 text-center shadow-sm">
        <div class="text-gray-600 mb-2">实时重量</div>
        <div class="text-4xl font-bold text-blue-600">{{ currentWeight }}g</div>
      </div>

      <!-- 校准步骤 -->
      <div class="bg-white rounded-lg p-6 shadow-sm">
        <div class="text-lg font-semibold mb-4 text-center">请先依次完成下列步骤</div>

        <!-- 步骤1 -->
        <div class="mb-6">
          <div class="flex items-center justify-between mb-3">
            <div class="flex items-center">
              <div
                class="w-8 h-8 rounded-full bg-blue-500 text-white flex items-center justify-center mr-3 text-sm font-bold"
              >
                1
              </div>
              <span class="text-lg">请清空杯架!</span>
            </div>
            <van-icon v-if="step1Completed" name="success" color="#07c160" size="24" />
          </div>
          <div class="ml-11">
            <van-button
              type="primary"
              size="large"
              :disabled="step1Completed || isLoading"
              :loading="isLoading && currentStep === 1"
              @click="executeStep1"
              class="w-full"
            >
              {{ step1Completed ? '步骤1已完成' : '完成' }}
            </van-button>
            <div v-if="step1Completed" class="mt-2 text-sm text-gray-500">
              重量: {{ step1Weight }}g
            </div>
          </div>
        </div>

        <!-- 步骤2 -->
        <div class="mb-6">
          <div class="flex items-center justify-between mb-3">
            <div class="flex items-center">
              <div
                class="w-8 h-8 rounded-full flex items-center justify-center mr-3 text-sm font-bold"
                :class="step1Completed ? 'bg-blue-500 text-white' : 'bg-gray-300 text-gray-500'"
              >
                2
              </div>
              <span class="text-lg" :class="step1Completed ? 'text-black' : 'text-gray-400'">
                请放入500g砝码
              </span>
            </div>
            <van-icon v-if="step2Completed" name="success" color="#07c160" size="24" />
          </div>
          <div class="ml-11">
            <van-button
              type="primary"
              size="large"
              :disabled="!step1Completed || step2Completed || isLoading"
              :loading="isLoading && currentStep === 2"
              @click="executeStep2"
              class="w-full"
            >
              {{ step2Completed ? '步骤2已完成' : '完成' }}
            </van-button>
            <div v-if="step2Completed" class="mt-2 text-sm text-gray-500">
              重量: {{ step2Weight }}g
            </div>
          </div>
        </div>

        <!-- 保存按钮 -->
        <div class="mt-8">
          <van-button
            type="success"
            size="large"
            :disabled="!allStepsCompleted || isSaving"
            :loading="isSaving"
            @click="saveCalibration"
            class="w-full"
          >
            保存校准结果
          </van-button>
        </div>
      </div>
    </div>

    <!-- 全屏加载遮罩 -->
    <van-overlay :show="isLoading" z-index="2000">
      <van-loading vertical class="overlay-content">
        <template #icon>
          <van-icon name="setting-o" size="30" />
        </template>
        {{ loadingText }}
      </van-loading>
    </van-overlay>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { showToast } from 'vant'
import router from '@/router'
import { getCurrentWeight, setWeight, saveWeightCalibration } from '@/api/send'

// 响应式数据
const currentWeight = ref(0)
const step1Completed = ref(false)
const step2Completed = ref(false)
const step1Weight = ref(0)
const step2Weight = ref(0)
const isLoading = ref(false)
const isSaving = ref(false)
const currentStep = ref(0)
const loadingText = ref('')

// 计算属性
const allStepsCompleted = computed(() => step1Completed.value && step2Completed.value)

// 实时重量更新定时器
let weightTimer: ReturnType<typeof setInterval> | null = null

// 返回上一页
const goBack = () => {
  router.back()
}

// 开始实时重量监控
const startWeightMonitoring = () => {
  weightTimer = setInterval(async () => {
    try {
      const weight = await getCurrentWeight()
      currentWeight.value = weight
    } catch (error) {
      console.warn('获取实时重量失败:', error)
    }
  }, 1000) // 每秒更新一次
}

// 停止实时重量监控
const stopWeightMonitoring = () => {
  if (weightTimer) {
    clearInterval(weightTimer)
    weightTimer = null
  }
}

// 执行步骤1 - 清空杯架并归零
const executeStep1 = async () => {
  if (step1Completed.value || isLoading.value) return

  isLoading.value = true
  currentStep.value = 1
  loadingText.value = '正在执行步骤1，请稍候...'

  try {
    // 先归零称重
    const setSuccess = await setWeight('0')
    if (!setSuccess) {
      throw new Error('归零失败')
    }

    // 等待一下让重量稳定
    await new Promise((resolve) => setTimeout(resolve, 1000))

    // 获取归零后的重量
    const weight = await getCurrentWeight()
    step1Weight.value = weight
    step1Completed.value = true
    showToast({ message: '步骤1完成 - 杯架已清空并归零', type: 'success' })
  } catch (error) {
    console.error('步骤1执行失败:', error)
    showToast({ message: '步骤1执行失败，请重试', type: 'fail' })
  } finally {
    isLoading.value = false
    currentStep.value = 0
  }
}

// 执行步骤2 - 放入500g砝码并记录重量
const executeStep2 = async () => {
  if (!step1Completed.value || step2Completed.value || isLoading.value) return

  isLoading.value = true
  currentStep.value = 2
  loadingText.value = '正在执行步骤2，请稍候...'

  try {
    // 等待用户放入砝码后获取重量
    await new Promise((resolve) => setTimeout(resolve, 2000))

    // 获取当前重量（应该是500g砝码的重量）
    const weight = await getCurrentWeight()
    step2Weight.value = weight
    step2Completed.value = true
    showToast({ message: '步骤2完成 - 已记录砝码重量', type: 'success' })
  } catch (error) {
    console.error('步骤2执行失败:', error)
    showToast({ message: '步骤2执行失败，请重试', type: 'fail' })
  } finally {
    isLoading.value = false
    currentStep.value = 0
  }
}

// 保存校准结果
const saveCalibration = async () => {
  if (!allStepsCompleted.value || isSaving.value) return

  isSaving.value = true

  try {
    const success = await saveWeightCalibration(step1Weight.value, step2Weight.value)

    if (success) {
      showToast({ message: '校准结果保存成功', type: 'success' })
      // 延迟返回上一页
      setTimeout(() => {
        goBack()
      }, 1500)
    } else {
      showToast({ message: '保存失败，请重试', type: 'fail' })
    }
  } catch (error) {
    console.error('保存校准结果失败:', error)
    showToast({ message: '保存失败，请重试', type: 'fail' })
  } finally {
    isSaving.value = false
  }
}

// 组件挂载时开始监控重量
onMounted(() => {
  startWeightMonitoring()
})

// 组件卸载时停止监控
onUnmounted(() => {
  stopWeightMonitoring()
})
</script>

<style scoped>
.overlay-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: white;
}
</style>
