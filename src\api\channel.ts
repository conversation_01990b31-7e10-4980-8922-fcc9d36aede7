import request from '../utils/axios'

/**
 * Request
 */
export interface channelRes {
  channel_no: string
  created_at: string
  id: number
  max_capacity: number
  mode: string
  mode_name: string
  name: string
  updated_at: string
  warn_capacity: number
}

export interface channelReq {
  channel_no: string
  max_capacity: number
  mode: string
  mode_name: string

  name: string
  warn_capacity: number
}

export const getChannels = async (): Promise<channelRes[]> => {
  return request.get('/channel')
}

export const deleteChannel = async (channel: channelRes) => {
  return request.delete(`/channel/${channel.id}`)
}

export const addChannel = (channel: Omit<channelReq, 'id'>) => {
  return request.post('/channel', channel)
}

export const updateChannel = (channel: channelRes) => {
  return request.put(`/channel/${channel.id}`, channel)
}
