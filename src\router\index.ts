import { createRouter, createWebHistory } from 'vue-router'
import HomeView from '@/views/HomeView.vue'
import AdminView from '@/views/AdminView.vue'
import ChannelManagementView from '@/views/ChannelManagementView.vue'
import WeightCalibrationView from '@/views/WeightCalibrationView.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'home',
      component: HomeView,
    },
    {
      path: '/admin',
      name: 'admin',
      component: AdminView,
    },
    {
      path: '/admin/channel-management',
      name: 'channel-management',
      component: ChannelManagementView,
    },
    {
      path: '/admin/weight-calibration',
      name: 'weight-calibration',
      component: WeightCalibrationView,
    },
  ],
})

export default router
