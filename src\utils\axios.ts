import axios from 'axios'
import { showFailToast } from 'vant'

// 定义接口响应格式
interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
}

const instance = axios.create({
  baseURL: 'http://localhost:8080',
  timeout: 5000,
})

// 请求拦截器
instance.interceptors.request.use(
  (config) => {
    // 可以在这里添加token等认证信息
    return config
  },
  (error) => {
    return Promise.reject(error)
  },
)

// 响应拦截器
instance.interceptors.response.use(
  (response) => {
    const res = response.data as ApiResponse

    // 判断业务状态码
    if (res.code === 0 || res.code === 200) {
      return res.data
    }

    // 业务层面的错误
    showFailToast(res.message || '操作失败')
    return Promise.reject(new Error(res.message || '操作失败'))
  },
  (error) => {
    // 网络层面的错误
    const message = error.response?.data?.message || error.message || '网络请求失败'
    showFailToast(message)
    return Promise.reject(error)
  },
)

export default instance
